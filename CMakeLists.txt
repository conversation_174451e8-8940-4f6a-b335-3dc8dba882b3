cmake_minimum_required(VERSION 3.20)

# VCPKG customization ....................................................

if (${CMAKE_TOOLCHAIN_FILE} MATCHES ".*vcpkg.cmake.*")
    message(STATUS "Building with vcpkg toolchain.")
    set(USING_VCPKG ON)
endif()


# Project setup ..........................................................

project(
    OSGEARTH
    DESCRIPTION "osgEarth SDK"
    HOMEPAGE_URL "https://github.com/gwaldron/osgearth"
    LANGUAGES CXX C)

# SDK version number
set(OSGEARTH_MAJOR_VERSION 3)
set(OSGEARTH_MINOR_VERSION 7)
set(OSGEARTH_PATCH_VERSION 3)
set(OSGEARTH_VERSION ${OSGEARTH_MAJOR_VERSION}.${OSGEARTH_MINOR_VERSION}.${OSGEARTH_PATCH_VERSION})

# Increment this each time the ABI changes
set(OSGEARTH_SOVERSION 175)

# Require C++14, don't fall back, and don't use compiler-specific extensions:
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Detect out-of-source build. We'll need this for protobuf generated files.
if (NOT "${PROJECT_SOURCE_DIR}" STREQUAL "${PROJECT_BINARY_DIR}")
    message(STATUS "Detected an out-of-source build. Kudos.")    
    set(OSGEARTH_OUT_OF_SOURCE_BUILD TRUE)
else()
    message(STATUS "Detected a in-source build.")  
endif()

# We have some custom .cmake scripts not in the official distribution.
set(CMAKE_MODULE_PATH "${CMAKE_MODULE_PATH};${PROJECT_SOURCE_DIR}/cmake")

# Special folder for build-time generated include files
set(OSGEARTH_BUILDTIME_INCLUDE_DIR "${CMAKE_CURRENT_BINARY_DIR}/build_include")
include_directories(${OSGEARTH_BUILDTIME_INCLUDE_DIR})

# Third-party sources included in the reposotory
set(OSGEARTH_EMBEDDED_THIRD_PARTY_DIR ${PROJECT_SOURCE_DIR}/src/third_party)

# Set output directories to redist_desk for direct deployment
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)

# For multi-config generators (Visual Studio)
foreach(OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES})
    string(TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
endforeach()

include(GNUInstallDirs)

if(BUILDING_VCPKG_PORT)
    # this path taken when vcpkg is building from the portfile.
    set(OSGEARTH_INSTALL_PLUGINSDIR "plugins")
    set(OSGEARTH_INSTALL_CMAKEDIR "${CMAKE_INSTALL_DATADIR}/osgearth")
else()
    if(WIN32)
        set(OSGEARTH_INSTALL_PLUGINSDIR "${CMAKE_INSTALL_BINDIR}" CACHE STRING "Parent folder of OSG plugins folder")
    else()
        set(OSGEARTH_INSTALL_PLUGINSDIR "${CMAKE_INSTALL_LIBDIR}" CACHE STRING "Parent folder of OSG plugins folder")
    endif()
    set(OSGEARTH_INSTALL_CMAKEDIR "${CMAKE_INSTALL_LIBDIR}/cmake/osgearth" CACHE STRING "osgEarth CMake package install directory")
endif()

set(OSGEARTH_INSTALL_DATADIR "${CMAKE_INSTALL_DATADIR}/osgearth" CACHE STRING "osgEarth data directory")

# Platform-specific settings ............................................

include(oe_ios)
include(oe_osx)
include(oe_unix)
include(oe_win32)

# Build options ..........................................................

option(OSGEARTH_BUILD_TOOLS "Build the osgEarth command-line tools" ON)
option(OSGEARTH_BUILD_EXAMPLES "Build the osgEarth example applications" ON)
option(OSGEARTH_BUILD_IMGUI_NODEKIT "Build the osgEarth ImGui nodekit and ImGui-based apps" ON)
option(OSGEARTH_BUILD_CESIUM_NODEKIT "Build the Cesium nodekit (osgEarthCesium)" OFF)
option(OSGEARTH_BUILD_TRITON_NODEKIT "Build support for SunDog Triton SDK" OFF)
option(OSGEARTH_BUILD_SILVERLINING_NODEKIT "Build support for SunDog SilverLining SDK" OFF)
option(OSGEARTH_ENABLE_GEOCODER "Enable the geocoder (requires external geocoding service)" OFF)

option(OSGEARTH_BUILD_DOCS "Include the documentation folder" ON)
mark_as_advanced(OSGEARTH_BUILD_DOCS)

option(OSGEARTH_BUILD_TESTS "Build the osgEarth unit tests" OFF)
mark_as_advanced(OSGEARTH_BUILD_TESTS)

option(OSGEARTH_BUILD_PROCEDURAL_NODEKIT "Build the procedural terrain nodekit (osgEarthProcedural)" OFF)
mark_as_advanced(OSGEARTH_BUILD_PROCEDURAL_NODEKIT)

option(OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT "Build the legacy procedural nodekit (osgEarthSplat)" OFF)
mark_as_advanced(OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT)

option(OSGEARTH_BUILD_LEGACY_CONTROLS_API "Build the legacy Controls UI API" OFF)
mark_as_advanced(OSGEARTH_BUILD_LEGACY_CONTROLS_API)

option(OSGEARTH_BUILD_ZIP_PLUGIN "Build osgEarth's zip plugin based on libzip" ON)
mark_as_advanced(OSGEARTH_BUILD_ZIP_PLUGIN)

option(OSGEARTH_ENABLE_PROFILING "Enable profiling with Tracy" OFF)
mark_as_advanced(OSGEARTH_ENABLE_PROFILING)

option(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT "Assume the use of a single GL context for all GL objects (advanced)" OFF)
mark_as_advanced(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT)

option(OSGEARTH_ASSUME_SINGLE_THREADED_OSG "Assume OSG will always be configured to run in SingleThreaded mode (advanced)" OFF)
mark_as_advanced(OSGEARTH_ASSUME_SINGLE_THREADED_OSG)

option(OSGEARTH_INSTALL_SHADERS "Whether to deploy GLSL shaders when installing (OFF=inlined shaders)" OFF)
mark_as_advanced(OSGEARTH_INSTALL_SHADERS)

if(WIN32)
    option(OSGEARTH_INSTALL_PDBS "Whether to deploy Windows .pdb files" OFF)
endif()


# Shared v. static build .................................................

option(OSGEARTH_BUILD_SHARED_LIBS "ON to build shared libraries; OFF to build static libraries." ON)
if(OSGEARTH_BUILD_SHARED_LIBS)
    set(OSGEARTH_DYNAMIC_OR_STATIC "SHARED")
else()
    set(OSGEARTH_DYNAMIC_OR_STATIC "STATIC")
endif()
message(STATUS "Building ${OSGEARTH_DYNAMIC_OR_STATIC} libraries")


# Whether to append SOVERSIONs to libraries (unix)
option(OSGEARTH_SONAMES "ON to append so-version numbers to libraries" ON)


# Dependencies ...........................................................

# Update git submodules
# https://cliutils.gitlab.io/modern-cmake/chapters/projects/submodule.html
find_package(Git QUIET)
if(GIT_FOUND AND EXISTS "${PROJECT_SOURCE_DIR}/.git")
    message(STATUS "Submodule update")
    execute_process(COMMAND ${GIT_EXECUTABLE} submodule update --init --recursive
                    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
                    RESULT_VARIABLE GIT_SUBMOD_RESULT)
    if(NOT GIT_SUBMOD_RESULT EQUAL "0")
        message(FATAL_ERROR "git submodule update --init --recursive failed with ${GIT_SUBMOD_RESULT}, please checkout submodules")
    endif()
endif()

# required - globally used
find_package(OpenGL REQUIRED)
find_package(OpenSceneGraph REQUIRED COMPONENTS osgManipulator osgShadow osgSim osgViewer osgGA osgUtil osgText osgDB osg OpenThreads)

\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/CropFilter.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  DrawInstanced.cpp
  (编译源文件“../../../src/osgEarth/Cube.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ECEF.cpp
  EarthManipulator.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DecalLayer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/CullingUtils.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DrapeableNode.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  Elevation.cpp
  ElevationLOD.cpp
  ElevationLayer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DrapingTechnique.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ElevationPool.cpp
  ElevationQuery.cpp
  ElevationRanges.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DepthOffset.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DrapingCullSet.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\osgUtil 
  \CullVisitor(469,31):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Draggers.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  EllipseNode.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ECEF.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  Ellipsoid.cpp
  Ephemeris.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/DrawInstanced.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Elevation.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ExampleResources.cpp
  Expression.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ElevationLOD.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ElevationQuery.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Ellipsoid.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\Ellipsoid.cpp(124,5):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ElevationRanges.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  Extension.cpp
  ExtrudeGeometryFilter.cpp
  (编译源文件“../../../src/osgEarth/EarthManipulator.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ElevationPool.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Ephemeris.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ExtrusionSymbol.cpp
  FadeEffect.cpp
  Feature.cpp
  FeatureCursor.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ElevationLayer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FeatureDisplayLayout.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Expression.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/EllipseNode.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FeatureModelGraph.cpp
  FeatureModelLayer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Extension.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FeatureModelSource.cpp
  FeatureNode.cpp
  FeatureRasterizer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ExtrusionSymbol.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FeatureSDFLayer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FadeEffect.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ExampleResources.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\vcpkg_installed\x64-windows\include\osgUtil
  \CullVisitor(469,31):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FeatureSource.cpp
  FeatureSourceIndexNode.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ExtrudeGeometryFilter.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用
  
  FeatureStyleSorter.cpp
  FileUtils.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Feature.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  Fill.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureDisplayLayout.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureRasterizer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Fill.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureCursor.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  Filter.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureNode.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureModelGraph.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FileUtils.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureModelLayer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FilterContext.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureSDFLayer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FilteredFeatureSource.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureModelSource.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FlatteningLayer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureSourceIndexNode.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureSource.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  FrameClock.cpp
  GARSGraticule.cpp
  GEOS.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FeatureStyleSorter.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GLSLChunker.cpp
  GLUtils.cpp
  GeoData.cpp
  GeoJSONReader.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GLSLChunker.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeoMath.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath.cpp(1,1): warning C4828: 文件包含在偏移 0x165a 处开始
的字符，该字符在当前源字符集中无效(代码页 65001)。 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.
vcxproj]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoMath.cpp(1,1): warning C4828: 文件包含在偏移 0x1679 处开始
的字符，该字符在当前源字符集中无效(代码页 65001)。 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.
vcxproj]
  GeoPositionNode.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Filter.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeoPositionNodeAutoScaler.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GEOS.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeoData.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GLUtils.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeoTransform.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FlatteningLayer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeoMath.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FilteredFeatureSource.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeodeticGraticule.cpp
  GeodeticLabelingEngine.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/FilterContext.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GARSGraticule.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeographicLibAdapter.cpp
  Geoid.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeoJSONReader.cpp(544,21): warning C4530: 使用了 C++ 异
常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxpr
oj]
  Geometry.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(39,5): warning C4530: 使用 了
C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.
vcxproj]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(369,45): error C2838: “Geo
graphic”: 成员声明中的限定名称非法 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj
]
  GeometryClamper.cpp
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(369,45): error C2065: “Geo
graphic”: 未声明的标识符 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(369,93): error C2838: “Web
Mercator”: 成员声明中的限定名称非法 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxpro
j]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(369,93): error C2065: “Web
Mercator”: 未声明的标识符 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(373,50): error C2838: “Web
Mercator”: 成员声明中的限定名称非法 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxpro
j]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(373,50): error C2065: “Web
Mercator”: 未声明的标识符 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(373,99): error C2838: “Geo
graphic”: 成员声明中的限定名称非法 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj
]
F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\GeographicLibAdapter.cpp(373,99): error C2065: “Geo 
graphic”: 未声明的标识符 [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth\osgEarth.vcxproj]
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeoPositionNode.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeometryCloud.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeoTransform.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeometryCompiler.cpp
  GeometryFactory.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeoPositionNodeAutoScaler.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeodeticLabelingEngine.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeometryRasterizer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeodeticGraticule.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  GeometryUtils.cpp
  GeosFeatureSource.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  GraticuleLabelingEngine.cpp
  (编译源文件“../../../src/osgEarth/Geometry.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Geoid.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  HTM.cpp
  HTTPClient.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryFactory.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryClamper.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  HeightFieldUtils.cpp
  Horizon.cpp
  HorizonClipPlane.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryUtils.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  IOTypes.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryCloud.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  IconResource.cpp
  IconSymbol.cpp
  ImageLayer.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/HTTPClient.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C 
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryCompiler.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeometryRasterizer.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GeosFeatureSource.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ImageMosaic.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/HTM.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ImageOverlay.cpp
C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/Horizon.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/HeightFieldUtils.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/IconResource.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/IconSymbol.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/IOTypes.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/ImageMosaic.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/HorizonClipPlane.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9): warning C
4530: 使用了 C++ 异常处理程序，但未启用展开语义。请指定 /EHsc [F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\build_desk\src\osgEarth
\osgEarth.vcxproj]
  (编译源文件“../../../src/osgEarth/GraticuleLabelingEngine.cpp”)
      C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Tools\MSVC\14.44.35207\include\__msvc_ostream.hpp(781,9):     
      模板实例化上下文(最早的实例化上下文)为
          F:\cmo-dev\my_osgearth_web\osgearth_origin\osgearth-3.7.2-wasm\src\osgEarth\StringUtils(293,77):
          查看对正在编译的函数 模板 实例化“std::basic_ostream<char,std::char_traits<char>> &std::operator <<<std::char_traits<char>>(std::basi
  c_ostream<char,std::char_traits<char>> &,const char *)”的引用

  ImageOverlayEditor.cpp
  ImageToFeatureLayer.cpp
  ImageUtils.cpp

# For static builds we need to link with Fontconfig directly.
# There is an unknown issue where the Fc symbols are not found when linking executables statically.
if(NOT OSGEARTH_BUILD_SHARED_LIBS)
    find_package(Fontconfig QUIET)
    if (Fontconfig_FOUND)
        list(APPEND OPENSCENEGRAPH_LIBRARIES Fontconfig::Fontconfig)
    endif()
endif()

# integrated profiling with tracy?
if(OSGEARTH_ENABLE_PROFILING)
    find_package(Tracy)
    if(Tracy_FOUND)
        message(STATUS "Found Tracy. Enabling frame profiling.")
    endif()
endif()

# Find SuperLuminalAPI
find_package(SuperluminalAPI QUIET)

# optimization option:
if(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT)
    add_definitions(-DOSGEARTH_SINGLE_GL_CONTEXT)
endif()

# optimization option:
if(OSGEARTH_ASSUME_SINGLE_THREADED_OSG)
    add_definitions(-DOSGEARTH_SINGLE_THREADED_OSG)
endif()

# Bring in our utility macros that sub-projects will use to configure themselves.
include(cmake/osgearth-macros.cmake)
include(cmake/install-package-config-files.cmake)

# Detect the OSG version so we can append it to plugin DLLs just like OSG does.
detect_osg_version()

if(NOT OPENSCENEGRAPH_VERSION)
	set(OPENSCENEGRAPH_VERSION ${OPENSCENEGRAPH_MAJOR_VERSION}.${OPENSCENEGRAPH_MINOR_VERSION}.${OPENSCENEGRAPH_PATCH_VERSION})
endif()
message(STATUS "Found OSG version ${OPENSCENEGRAPH_VERSION}")

# Make the headers visible to everything
include_directories(
    ${OSGEARTH_SOURCE_DIR}/src
    ${OPENSCENEGRAPH_INCLUDE_DIR})
    
# If OSG is built with OPENGL_PROFILE=GLCORE, <osg/GL> will try to include the GLCORE ARB headers.
# Set this variable to make those headers available when building osgEarth.
set(OSGEARTH_GLCORE_INCLUDE_DIR "" CACHE PATH "Location of OpenGL CORE profile header parent folder")
mark_as_advanced(OSGEARTH_GLCORE_INCLUDE_DIR)
if(OSGEARTH_GLCORE_INCLUDE_DIR)
    include_directories(${OSGEARTH_GLCORE_INCLUDE_DIR})
endif()


# Source code ............................................................

add_subdirectory(src)

# 添加测试程序
add_executable(test_xyz_fix test_xyz_fix.cpp)
target_link_libraries(test_xyz_fix osgEarth ${OSG_LIBRARIES} ${OPENTHREADS_LIBRARIES})
target_include_directories(test_xyz_fix PRIVATE src)

# 添加LOD逻辑测试程序
add_executable(test_lod_logic test_lod_logic.cpp)
target_link_libraries(test_lod_logic osgEarth ${OSG_LIBRARIES} ${OPENTHREADS_LIBRARIES})
target_include_directories(test_lod_logic PRIVATE src)

if(OSGEARTH_BUILD_DOCS)
    add_subdirectory(docs)
endif()
    
    
# IDE configuration ......................................................

set_property(GLOBAL PROPERTY USE_FOLDERS ON)
set_property(GLOBAL PROPERTY PREDEFINED_TARGETS_FOLDER "CMake Targets")


# Uninstall target .......................................................

configure_file(
   "${CMAKE_CURRENT_SOURCE_DIR}/cmake/cmake_uninstall.cmake.in"
   "${CMAKE_CURRENT_BINARY_DIR}/uninstall.cmake"
   IMMEDIATE @ONLY
)

add_custom_target(uninstall
   "${CMAKE_COMMAND}" -P "${CMAKE_CURRENT_BINARY_DIR}/uninstall.cmake"
)
